url_天气预报 = 'https://api.open-meteo.com/v1/forecast?latitude=40.75&longitude=95.75&models=gfs_seamless,jma_seamless,kma_seamless,ecmwf_ifs025,gfs_global,jma_msm,kma_ldps,kma_gdps,jma_gsm,gfs_hrrr,ecmwf_aifs025_single,cma_grapes_global,ncep_nbm_conus,gfs_graphcast025,bom_access_global,icon_seamless,gem_seamless,italia_meteo_arpae_icon_2i,meteofrance_seamless,icon_global,gem_global,meteofrance_arpege_world,knmi_seamless,ukmo_seamless,ukmo_global_deterministic_10km,ukmo_uk_deterministic_2km,dmi_seamless,gem_hrdps_continental&minutely_15=wind_speed_10m,wind_speed_80m,wind_gusts_10m&timezone=Asia%2FSingapore&past_days=1&forecast_days=7&wind_speed_unit=ms&format=csv'
# url_历史天气 = 'https://historical-forecast-api.open-meteo.com/v1/forecast?latitude=40.75&longitude=95.75&start_date=2024-02-01&end_date=2025-07-26&models=gfs_seamless,jma_seamless,kma_seamless,ecmwf_ifs025,gfs_global,jma_msm,kma_ldps,kma_gdps,jma_gsm,gfs_hrrr,ecmwf_aifs025_single,cma_grapes_global,ncep_nbm_conus,gfs_graphcast025,bom_access_global,icon_seamless,gem_seamless,italia_meteo_arpae_icon_2i,meteofrance_seamless,icon_global,gem_global,meteofrance_arpege_world,knmi_seamless,ukmo_seamless,ukmo_global_deterministic_10km,ukmo_uk_deterministic_2km,dmi_seamless,gem_hrdps_continental&minutely_15=wind_speed_10m,wind_speed_80m,wind_gusts_10m&timezone=Asia%2FSingapore&wind_speed_unit=ms&format=csv'
    # 定义需要保留的列
columns_to_keep = [
        '时间',
        'wind_speed_10m_gfs_seamless (m/s)',
        'wind_speed_80m_gfs_seamless (m/s)',
        'wind_gusts_10m_gfs_seamless (m/s)',
        'wind_speed_10m_jma_seamless (m/s)',
        'wind_speed_10m_gfs_global (m/s)',
        'wind_speed_80m_gfs_global (m/s)',
        'wind_gusts_10m_gfs_global (m/s)',
        'wind_speed_10m_jma_gsm (m/s)',
        'wind_speed_10m_cma_grapes_global (m/s)',
        'wind_speed_80m_cma_grapes_global (m/s)',
        'wind_gusts_10m_cma_grapes_global (m/s)',
        'wind_speed_10m_icon_seamless (m/s)',
        'wind_speed_80m_icon_seamless (m/s)',
        'wind_gusts_10m_icon_seamless (m/s)',
        'wind_speed_10m_gem_seamless (m/s)',
        'wind_speed_80m_gem_seamless (m/s)',
        'wind_gusts_10m_gem_seamless (m/s)',
        'wind_speed_10m_meteofrance_seamless (m/s)',
        'wind_speed_80m_meteofrance_seamless (m/s)',
        'wind_gusts_10m_meteofrance_seamless (m/s)',
        'wind_speed_10m_icon_global (m/s)',
        'wind_speed_80m_icon_global (m/s)',
        'wind_gusts_10m_icon_global (m/s)',
        'wind_speed_10m_gem_global (m/s)',
        'wind_speed_80m_gem_global (m/s)',
        'wind_gusts_10m_gem_global (m/s)',
        'wind_speed_10m_meteofrance_arpege_world (m/s)',
        'wind_speed_80m_meteofrance_arpege_world (m/s)',
        'wind_gusts_10m_meteofrance_arpege_world (m/s)',
        'wind_speed_10m_ukmo_seamless (m/s)',
        'wind_gusts_10m_ukmo_seamless (m/s)',
        'wind_speed_10m_ukmo_global_deterministic_10km (m/s)',
        'wind_gusts_10m_ukmo_global_deterministic_10km (m/s)'
    ]
#下载url_天气预报，并保存到data/天气预报/站点C_多模型_天气预报_当前日期时间.csv
import pandas as pd
import requests
import datetime

def download_weather_forecast(url, save_path):
    response = requests.get(url)
    with open(save_path, 'wb') as f:
        f.write(response.content)
        print(f'✅ 天气预报数据已保存到 {save_path}')
        #跳过前3条
        df = pd.read_csv(save_path, skiprows=3)
        #重命名时间列
        df.rename(columns={'time': '时间'}, inplace=True)
        df = df[columns_to_keep]
        df.to_csv(save_path, index=False)
        print(f'数据形状: {df.shape}')

if __name__ == '__main__':
    now = datetime.datetime.now()
    current_time = now.strftime("%Y%m%d_%H%M%S")
    save_path = f'data/天气预报/点位c_多网站_天气预报_{current_time}.csv'
    download_weather_forecast(url_天气预报, save_path)
