"""
诊断多源风速数据应用情况
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import sys

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager

def diagnose_multi_source_application():
    """诊断多源风速数据应用情况"""
    print("=" * 60)
    print("多源风速数据应用诊断")
    print("=" * 60)
    
    # 1. 检查配置
    print("1. 检查配置...")
    config = ConfigManager()
    multi_source_config = config.get('multi_source_wind', {})
    
    print(f"   多源风速功能启用: {multi_source_config.get('enable', False)}")
    print(f"   训练文件: {multi_source_config.get('train_file', 'N/A')}")
    print(f"   测试文件: {multi_source_config.get('test_file', 'N/A')}")
    
    # 2. 检查训练模型的特征
    print("\n2. 检查训练模型的特征...")
    feature_file = Path("production_model/feature_columns_20250728_115501.json")
    
    if feature_file.exists():
        with open(feature_file, 'r', encoding='utf-8') as f:
            features = json.load(f)
        
        # 统计多源特征
        multi_features = [f for f in features if 'multi_' in f or any(model in f for model in ['gfs_', 'jma_', 'cma_', 'icon_', 'gem_', 'meteofrance_', 'ukmo_'])]
        
        print(f"   总特征数: {len(features)}")
        print(f"   多源特征数: {len(multi_features)}")
        print(f"   多源特征占比: {len(multi_features)/len(features)*100:.1f}%")
        
        print(f"\n   前10个多源特征:")
        for i, feature in enumerate(multi_features[:10], 1):
            print(f"     {i:2d}. {feature}")
        
        if len(multi_features) > 10:
            print(f"     ... 还有 {len(multi_features)-10} 个多源特征")
    else:
        print("   ERROR: 特征文件不存在")
        return False
    
    # 3. 检查测试数据文件
    print("\n3. 检查测试数据文件...")
    test_data_dir = config.get('data_files.test_data_directory', 'data/testing')
    test_file = multi_source_config.get('test_file', '站点c_多网站历史风速202503.csv')
    test_path = Path(test_data_dir) / test_file
    
    print(f"   测试数据路径: {test_path}")
    print(f"   测试数据存在: {test_path.exists()}")
    
    if test_path.exists():
        try:
            # 尝试加载测试数据
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            test_df = None
            
            for encoding in encodings:
                try:
                    test_df = pd.read_csv(test_path, encoding=encoding)
                    break
                except:
                    continue
            
            if test_df is not None:
                print(f"   测试数据形状: {test_df.shape}")
                print(f"   测试数据列数: {len(test_df.columns)}")
                
                # 检查风速列
                wind_cols = [col for col in test_df.columns if 'wind_speed' in col]
                print(f"   风速列数: {len(wind_cols)}")
                
                # 检查时间范围
                if '时间' in test_df.columns:
                    test_df['时间'] = pd.to_datetime(test_df['时间'])
                    print(f"   时间范围: {test_df['时间'].min()} 到 {test_df['时间'].max()}")
                elif 'ʱ��' in test_df.columns:
                    test_df['ʱ��'] = pd.to_datetime(test_df['ʱ��'])
                    print(f"   时间范围: {test_df['ʱ��'].min()} 到 {test_df['ʱ��'].max()}")
            else:
                print("   ERROR: 无法加载测试数据")
                return False
        except Exception as e:
            print(f"   ERROR: 测试数据加载失败: {e}")
            return False
    else:
        print("   ERROR: 测试数据文件不存在")
        return False
    
    # 4. 模拟预测流程检查
    print("\n4. 模拟预测流程检查...")
    try:
        from src.multi_source_wind_fusion import MultiSourceWindFusion
        from src.comprehensive_feature_engine import ComprehensiveFeatureEngine
        
        # 创建融合器
        fusion = MultiSourceWindFusion(config)
        feature_engine = ComprehensiveFeatureEngine(config)
        
        # 加载少量测试数据
        test_sample = test_df.head(10).copy()
        print(f"   使用样本数据: {test_sample.shape}")
        
        # 执行多源数据融合
        multi_source_features = fusion.fuse_multi_source_data(test_sample)
        print(f"   多源特征生成: {multi_source_features.shape}")
        print(f"   多源特征数量: {len(multi_source_features.columns)}")
        
        # 创建模拟主数据
        main_data = pd.DataFrame({
            '时间': test_sample['时间'] if '时间' in test_sample.columns else test_sample['ʱ��'],
            'wind_speed_10m': np.random.uniform(5, 15, len(test_sample)),
            'wind_speed_80m': np.random.uniform(6, 18, len(test_sample)),
            'temperature_2m': np.random.uniform(10, 25, len(test_sample)),
            'rain': np.random.uniform(0, 2, len(test_sample)),
        })
        
        # 执行综合特征工程
        result = feature_engine.process_comprehensive_features(
            main_data,
            multi_source_data=test_sample,
            enable_multi_source=True
        )
        
        print(f"   最终特征数量: {len(result.columns)}")
        
        # 检查是否包含多源特征
        result_multi_features = [col for col in result.columns if 'multi_' in col or any(model in col for model in ['gfs_', 'jma_', 'cma_', 'icon_', 'gem_', 'meteofrance_', 'ukmo_'])]
        print(f"   结果中多源特征数: {len(result_multi_features)}")
        
        if len(result_multi_features) > 0:
            print("   SUCCESS: 预测流程正确应用了多源特征")
            print(f"   前5个多源特征: {result_multi_features[:5]}")
            return True
        else:
            print("   ERROR: 预测流程未应用多源特征")
            return False
            
    except Exception as e:
        print(f"   ERROR: 预测流程检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_prediction_consistency():
    """检查预测一致性"""
    print("\n" + "=" * 60)
    print("预测一致性检查")
    print("=" * 60)
    
    # 检查最近的预测文件
    prediction_dir = Path("daily_predictions")
    if not prediction_dir.exists():
        print("ERROR: 预测目录不存在")
        return False
    
    # 获取最近的两个预测文件
    prediction_files = sorted(prediction_dir.glob("今日预测_*.csv"))
    
    if len(prediction_files) < 2:
        print("ERROR: 预测文件不足，无法比较")
        return False
    
    # 比较最近的两个预测
    latest_file = prediction_files[-1]
    previous_file = prediction_files[-2]
    
    print(f"比较文件:")
    print(f"  最新: {latest_file.name}")
    print(f"  之前: {previous_file.name}")
    
    try:
        latest_df = pd.read_csv(latest_file)
        previous_df = pd.read_csv(previous_file)
        
        print(f"最新预测形状: {latest_df.shape}")
        print(f"之前预测形状: {previous_df.shape}")
        
        # 检查预测值是否相同
        if 'predicted_power' in latest_df.columns and 'predicted_power' in previous_df.columns:
            latest_values = latest_df['predicted_power'].values
            previous_values = previous_df['predicted_power'].values
            
            if len(latest_values) == len(previous_values):
                # 计算差异
                diff = np.abs(latest_values - previous_values)
                max_diff = np.max(diff)
                mean_diff = np.mean(diff)
                
                print(f"预测值差异:")
                print(f"  最大差异: {max_diff:.6f}")
                print(f"  平均差异: {mean_diff:.6f}")
                
                if max_diff < 1e-10:
                    print("  WARNING: 预测值完全相同，可能未应用多源数据")
                    return False
                else:
                    print("  OK: 预测值有差异，说明模型在更新")
                    return True
            else:
                print("  ERROR: 预测文件长度不同")
                return False
        else:
            print("  ERROR: 预测文件缺少predicted_power列")
            return False
            
    except Exception as e:
        print(f"ERROR: 预测文件比较失败: {e}")
        return False

def main():
    """主函数"""
    success1 = diagnose_multi_source_application()
    success2 = check_prediction_consistency()
    
    print("\n" + "=" * 60)
    print("诊断总结")
    print("=" * 60)
    
    if success1 and success2:
        print("SUCCESS: 多源风速数据应用正常")
        print("- 训练模型包含多源特征")
        print("- 预测流程正确应用多源数据")
        print("- 预测结果有合理变化")
    elif success1 and not success2:
        print("PARTIAL: 多源数据配置正确，但预测结果可能相同")
        print("- 训练模型包含多源特征")
        print("- 预测流程正确应用多源数据")
        print("- WARNING: 预测结果可能未体现多源数据的影响")
        print("\n可能原因:")
        print("1. 多源数据的时间范围与测试数据不匹配")
        print("2. 多源特征的权重较小，影响不明显")
        print("3. 需要重新训练模型以充分利用多源特征")
    else:
        print("FAILED: 多源风速数据应用存在问题")
        if not success1:
            print("- 多源数据配置或应用有问题")
        if not success2:
            print("- 预测结果完全相同")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
