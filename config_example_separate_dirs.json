{"data_files": {"data_directory": "data", "train_data_directory": "data/training", "test_data_directory": "data/testing"}, "features": {"time_column": "时间", "target_column": "理论功率 (MW)", "feature_columns": ["wind_speed_10m", "wind_speed_80m", "wind_direction_10m", "wind_direction_80m", "wind_gusts_10m", "temperature_2m", "rain", "apparent_temperature"]}, "model_settings": {"cv_folds": 5, "n_trials": 50, "physics_constrained": true, "random_state": 42}, "feature_engineering": {"mode": 4, "mode_description": {"1": "传统特征工程 + 特征选择", "2": "综合特征工程 + 特征选择", "3": "传统特征工程 + 综合特征工程 + 特征选择", "4": "多点位融合 + 传统特征工程 + 综合特征工程 + 特征选择"}}, "multi_location": {"enable": true, "target_power_file": "点位c_发电功率.csv", "locations": {"c": [40.75, 95.75], "1": [40.625, 95.625], "2": [40.625, 95.875], "3": [40.875, 95.625], "4": [40.875, 95.875]}, "distance_alpha": 2.0, "min_weight": 0.01, "train_file_pattern": "15min_历史气象20250228之前", "test_file_pattern": "15min_历史气象202503"}, "feature_selection": {"enable_feature_selection": true, "selected_features_count": 300, "selection_method": "importance", "importance_threshold": 0.001}, "output_directories": {"processed_data": "processed_data", "production_model": "production_model", "daily_predictions": "daily_predictions", "results": "results", "visualizations": "visualizations"}, "encoding": {"preferred_encodings": ["utf-8", "gbk", "gb2312", "utf-8-sig", "cp1252"], "auto_detect": true}}