#!/usr/bin/env python3
"""
测试集成特征工程系统
验证风机参数集成特征对预测准确率的提升效果
"""

import pandas as pd
import numpy as np
from src.integrated_feature_engineer import IntegratedFeatureEngineer
from src.data_loader import WindPowerDataLoader
import warnings
warnings.filterwarnings('ignore')

def test_integrated_features():
    """测试集成特征工程系统"""
    print("=" * 60)
    print("测试集成特征工程系统")
    print("=" * 60)
    
    try:
        # 1. 初始化集成特征工程器
        print("1. 初始化集成特征工程器...")
        engineer = IntegratedFeatureEngineer()
        print(f"   启用的模块: {engineer._get_enabled_modules()}")
        
        # 2. 加载测试数据
        print("\n2. 加载测试数据...")
        data_loader = WindPowerDataLoader()
        df = data_loader.load_training_data()
        print(f"   原始数据形状: {df.shape}")
        
        # 取一个小样本进行测试
        df_sample = df.head(100).copy()
        print(f"   测试样本形状: {df_sample.shape}")
        
        # 3. 执行集成特征工程
        print("\n3. 执行集成特征工程...")
        df_enhanced = engineer.engineer_features(df_sample, is_training=True)
        print(f"   增强后数据形状: {df_enhanced.shape}")
        print(f"   新增特征数量: {df_enhanced.shape[1] - df_sample.shape[1]}")
        
        # 4. 分析新特征
        print("\n4. 分析新特征...")
        new_features = set(df_enhanced.columns) - set(df_sample.columns)
        print(f"   新特征数量: {len(new_features)}")
        
        # 按类别统计新特征
        feature_categories = {
            '风机参数特征': [f for f in new_features if any(keyword in f for keyword in ['huarui', 'goldwind', 'theoretical_power', 'power_coefficient', 'thrust_coefficient'])],
            '物理约束特征': [f for f in new_features if any(keyword in f for keyword in ['density', 'turbulence', 'bias', 'correction'])],
            '时序增强特征': [f for f in new_features if any(keyword in f for keyword in ['lag', 'ma_', 'autocorr', 'hour', 'time'])],
            '其他特征': []
        }
        
        # 分类未分类的特征
        classified_features = set()
        for category_features in feature_categories.values():
            classified_features.update(category_features)
        
        feature_categories['其他特征'] = list(new_features - classified_features)
        
        for category, features in feature_categories.items():
            if features:
                print(f"   {category}: {len(features)}个")
                print(f"     示例: {features[:3]}")
        
        # 5. 获取特征重要性排序
        print("\n5. 特征重要性排序...")
        ranking = engineer.get_feature_importance_ranking()
        for priority, features in ranking.items():
            if features:
                print(f"   {priority}: {len(features)}个特征")
        
        # 6. 验证特征质量
        print("\n6. 验证特征质量...")
        numeric_cols = df_enhanced.select_dtypes(include=[np.number]).columns
        
        # 检查缺失值
        missing_count = df_enhanced[numeric_cols].isnull().sum().sum()
        print(f"   缺失值总数: {missing_count}")
        
        # 检查无穷大值
        inf_count = np.isinf(df_enhanced[numeric_cols]).sum().sum()
        print(f"   无穷大值总数: {inf_count}")
        
        # 检查常数特征
        constant_features = [col for col in numeric_cols if df_enhanced[col].nunique() <= 1]
        print(f"   常数特征数量: {len(constant_features)}")
        
        # 7. 保存特征信息
        print("\n7. 保存特征信息...")
        feature_info = {
            'original_features': len(df_sample.columns),
            'enhanced_features': len(df_enhanced.columns),
            'new_features_count': len(new_features),
            'feature_categories': {k: len(v) for k, v in feature_categories.items()},
            'quality_metrics': {
                'missing_values': int(missing_count),
                'infinite_values': int(inf_count),
                'constant_features': len(constant_features)
            }
        }
        
        import json
        with open('integrated_features_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(feature_info, f, indent=2, ensure_ascii=False)
        
        print("   测试报告已保存: integrated_features_test_report.json")
        
        print("\n" + "=" * 60)
        print("✅ 集成特征工程系统测试成功!")
        print("=" * 60)
        print(f"总结:")
        print(f"- 原始特征: {len(df_sample.columns)}个")
        print(f"- 增强特征: {len(df_enhanced.columns)}个")
        print(f"- 新增特征: {len(new_features)}个")
        print(f"- 特征质量: {'良好' if missing_count == 0 and inf_count == 0 else '需要优化'}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_consistency():
    """测试特征一致性"""
    print("\n" + "=" * 60)
    print("测试特征一致性")
    print("=" * 60)
    
    try:
        engineer = IntegratedFeatureEngineer()
        
        # 创建两个相同的测试数据集
        test_data = {
            '时间': pd.date_range('2025-03-01', periods=20, freq='15min'),
            'wind_speed_10m': np.random.uniform(2, 15, 20),
            'wind_speed_80m': np.random.uniform(3, 18, 20),
            'wind_direction_10m': np.random.uniform(0, 360, 20),
            'wind_direction_80m': np.random.uniform(0, 360, 20),
            'wind_gusts_10m': np.random.uniform(3, 20, 20),
            'temperature_2m': np.random.uniform(-5, 25, 20),
            'rain': np.random.uniform(0, 10, 20),
            'apparent_temperature': np.random.uniform(-8, 22, 20)
        }
        
        df1 = pd.DataFrame(test_data)
        df2 = pd.DataFrame(test_data)
        
        # 训练模式处理
        df1_enhanced = engineer.engineer_features(df1, is_training=True)
        
        # 预测模式处理
        df2_enhanced = engineer.engineer_features(df2, is_training=False)
        
        # 检查特征一致性
        features1 = set(df1_enhanced.columns)
        features2 = set(df2_enhanced.columns)
        
        missing_features = features1 - features2
        extra_features = features2 - features1
        
        print(f"训练模式特征数量: {len(features1)}")
        print(f"预测模式特征数量: {len(features2)}")
        print(f"缺失特征: {len(missing_features)}")
        print(f"额外特征: {len(extra_features)}")
        
        if len(missing_features) == 0 and len(extra_features) == 0:
            print("✅ 特征一致性测试通过!")
            return True
        else:
            print("❌ 特征一致性测试失败!")
            if missing_features:
                print(f"   缺失特征: {list(missing_features)[:5]}")
            if extra_features:
                print(f"   额外特征: {list(extra_features)[:5]}")
            return False
            
    except Exception as e:
        print(f"❌ 特征一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行测试
    success1 = test_integrated_features()
    success2 = test_feature_consistency()
    
    if success1 and success2:
        print("\n🎉 所有测试通过! 集成特征工程系统已准备就绪!")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置。")
