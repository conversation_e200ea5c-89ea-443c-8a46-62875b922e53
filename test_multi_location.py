"""
测试多点位数据融合系统
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

def test_multi_location_fusion():
    """测试多点位数据融合"""
    print("=" * 60)
    print("测试多点位数据融合系统")
    print("=" * 60)
    
    try:
        # 1. 测试导入
        print("1. 测试模块导入...")
        from multi_location_fusion import MultiLocationDataFusion
        from spatial_feature_engine import SpatialFeatureEngine
        print("  ✅ 模块导入成功")
        
        # 2. 创建融合器
        print("\n2. 创建多点位融合器...")
        fusion = MultiLocationDataFusion()
        spatial_engine = SpatialFeatureEngine()
        print("  ✅ 融合器创建成功")
        
        # 3. 测试权重计算
        print("\n3. 测试空间权重计算...")
        weights = fusion.calculate_spatial_weights()
        print(f"  空间权重: {weights}")
        print("  ✅ 权重计算成功")
        
        # 4. 测试数据加载
        print("\n4. 测试多点位数据加载...")
        try:
            data_dir = "data"
            file_pattern = "15min_历史气象20250228之前"
            
            location_data = fusion.load_location_data(data_dir, file_pattern, is_train=True)
            print(f"  成功加载 {len(location_data)} 个点位的数据")
            
            for loc_id, df in location_data.items():
                print(f"    点位{loc_id}: {len(df)} 条记录")
            
            print("  ✅ 数据加载成功")
            
            # 5. 测试时间对齐
            print("\n5. 测试时间序列对齐...")
            aligned_data = fusion.align_time_series(location_data)
            print(f"  对齐后数据点数: {len(next(iter(aligned_data.values())))}")
            print("  ✅ 时间对齐成功")
            
            # 6. 测试特征融合
            print("\n6. 测试特征融合...")
            fusion_features = fusion.create_weighted_fusion_features(aligned_data)
            print(f"  融合特征数: {len(fusion_features.columns) - 1}")  # 减去时间列
            
            gradient_features = fusion.create_spatial_gradient_features(aligned_data)
            print(f"  梯度特征数: {len(gradient_features.columns) - 1}")
            
            statistics_features = fusion.create_spatial_statistics_features(aligned_data)
            print(f"  统计特征数: {len(statistics_features.columns) - 1}")
            
            print("  ✅ 特征融合成功")
            
            # 7. 测试完整融合流程
            print("\n7. 测试完整融合流程...")
            result_df = fusion.fuse_multi_location_data(data_dir, file_pattern, is_train=True)
            print(f"  最终数据形状: {result_df.shape}")
            print(f"  特征列数: {len(result_df.columns) - 1}")
            print("  ✅ 完整融合成功")
            
            # 8. 测试空间特征工程
            print("\n8. 测试空间特征工程...")
            enhanced_df = spatial_engine.process_spatial_features(result_df)
            print(f"  增强后数据形状: {enhanced_df.shape}")
            print(f"  新增特征数: {enhanced_df.shape[1] - result_df.shape[1]}")
            print("  ✅ 空间特征工程成功")
            
        except FileNotFoundError as e:
            print(f"  ⚠️ 数据文件未找到: {e}")
            print("  这是正常的，如果数据文件不存在")
        except Exception as e:
            print(f"  ❌ 数据处理失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("✅ 多点位数据融合系统测试通过!")
        print("=" * 60)
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_integration():
    """测试配置集成"""
    print("\n测试配置集成...")
    
    try:
        from config_manager import ConfigManager
        
        config = ConfigManager()
        
        # 检查多点位配置
        multi_config = config.get('multi_location', {})
        print(f"  多点位配置: {multi_config}")
        
        # 检查特征工程模式
        fe_mode = config.get('feature_engineering.mode', 3)
        print(f"  特征工程模式: {fe_mode}")
        
        print("  ✅ 配置集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    success = True
    
    # 测试多点位融合
    if not test_multi_location_fusion():
        success = False
    
    # 测试配置集成
    if not test_config_integration():
        success = False
    
    if success:
        print("\n🎉 所有测试通过! 多点位系统已准备就绪!")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
