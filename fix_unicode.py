"""
修复Unicode字符问题
"""

import re
from pathlib import Path

def fix_unicode_in_file(file_path):
    """修复文件中的Unicode字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Unicode字符
        replacements = {
            '✅': '',
            '❌': '',
            '✓': '',
            '🎯': '',
            '⚙️': '',
            '📚': '',
            '🔧': '',
            '📂': '',
            '📋': '',
            '🚀': '',
            '⚠️': '',
            '\u2713': ''
        }
        
        for unicode_char, replacement in replacements.items():
            content = content.replace(unicode_char, replacement)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"已修复: {file_path}")
        
    except Exception as e:
        print(f"修复失败 {file_path}: {e}")

def main():
    """主函数"""
    files_to_fix = [
        "src/config_manager.py",
        "predict_today.py",
        "predict_today_new.py",
        "src/data_quality_optimizer.py",
        "src/comprehensive_feature_engine.py",
        "src/advanced_time_features.py",
        "src/advanced_physics_features.py",
        "src/breakthrough_features.py",
        "src/feature_selector.py",
        "src/data_loader.py",
        "train_and_export_model.py"
    ]
    
    for file_path in files_to_fix:
        if Path(file_path).exists():
            fix_unicode_in_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
