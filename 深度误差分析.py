#!/usr/bin/env python3
"""
深度误差分析工具
专门用于分析风力发电功率预测的误差模式和特征
只负责分析数据，不提供具体改进建议
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DeepErrorAnalyzer:
    def __init__(self, data_file='详细评测数据.csv'):
        """初始化分析器"""
        self.data_file = data_file
        self.df = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        try:
            self.df = pd.read_csv(self.data_file, encoding='utf-8-sig')
            self.df['时间'] = pd.to_datetime(self.df['时间'])
            self.df['小时'] = self.df['时间'].dt.hour
            self.df['分钟'] = self.df['时间'].dt.minute
            self.df['星期'] = self.df['时间'].dt.dayofweek
            self.df['绝对误差'] = abs(self.df['误差'])
            self.df['误差百分比'] = abs(self.df['相对误差']) * 100
            print(f"数据加载成功，共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def basic_statistics(self):
        """基础统计分析"""
        print("\n" + "="*60)
        print("基础统计分析")
        print("="*60)
        
        stats_data = {
            '指标': ['预测值', '真实值', '误差', '绝对误差', '相对误差(%)', '绝对相对误差(%)'],
            '均值': [
                self.df['pred'].mean(),
                self.df['答案'].mean(), 
                self.df['误差'].mean(),
                self.df['绝对误差'].mean(),
                self.df['相对误差'].mean() * 100,
                self.df['误差百分比'].mean()
            ],
            '标准差': [
                self.df['pred'].std(),
                self.df['答案'].std(),
                self.df['误差'].std(), 
                self.df['绝对误差'].std(),
                self.df['相对误差'].std() * 100,
                self.df['误差百分比'].std()
            ],
            '最小值': [
                self.df['pred'].min(),
                self.df['答案'].min(),
                self.df['误差'].min(),
                self.df['绝对误差'].min(),
                self.df['相对误差'].min() * 100,
                self.df['误差百分比'].min()
            ],
            '最大值': [
                self.df['pred'].max(),
                self.df['答案'].max(),
                self.df['误差'].max(),
                self.df['绝对误差'].max(),
                self.df['相对误差'].max() * 100,
                self.df['误差百分比'].max()
            ]
        }
        
        stats_df = pd.DataFrame(stats_data)
        print(stats_df.round(4))
        self.analysis_results['基础统计'] = stats_df
        
        # 数据分布特征
        print(f"\n数据分布特征:")
        print(f"预测值范围: {self.df['pred'].min():.2f} ~ {self.df['pred'].max():.2f} MW")
        print(f"真实值范围: {self.df['答案'].min():.2f} ~ {self.df['答案'].max():.2f} MW")
        print(f"误差范围: {self.df['误差'].min():.2f} ~ {self.df['误差'].max():.2f} MW")
        print(f"平均绝对误差: {self.df['绝对误差'].mean():.2f} MW")
        print(f"平均绝对百分比误差: {self.df['误差百分比'].mean():.2f}%")
    
    def time_dimension_analysis(self):
        """时间维度分析"""
        print("\n" + "="*60)
        print("时间维度误差分析")
        print("="*60)
        
        # 按小时分析
        hourly_stats = self.df.groupby('小时').agg({
            '绝对误差': ['mean', 'std', 'count'],
            '误差百分比': ['mean', 'std'],
            '误差': ['mean', 'std']
        }).round(4)
        
        print("按小时误差统计:")
        print(hourly_stats)
        self.analysis_results['小时误差统计'] = hourly_stats
        
        # 按日期分析
        daily_stats = self.df.groupby('日期').agg({
            '绝对误差': ['mean', 'std', 'count'],
            '误差百分比': ['mean', 'std'],
            '误差': ['mean', 'std']
        }).round(4)
        
        print(f"\n按日期误差统计:")
        print(daily_stats)
        self.analysis_results['日期误差统计'] = daily_stats
        
        # 找出误差最大的时间段
        worst_hours = self.df.groupby('小时')['绝对误差'].mean().sort_values(ascending=False)
        best_hours = self.df.groupby('小时')['绝对误差'].mean().sort_values(ascending=True)
        
        print(f"\n误差最大的3个小时: {worst_hours.head(3).index.tolist()}")
        print(f"误差最小的3个小时: {best_hours.head(3).index.tolist()}")
    
    def error_distribution_analysis(self):
        """误差分布分析"""
        print("\n" + "="*60)
        print("误差分布分析")
        print("="*60)
        
        # 误差分布统计
        error_ranges = [
            (-float('inf'), -10), (-10, -5), (-5, -1), 
            (-1, 1), (1, 5), (5, 10), (10, float('inf'))
        ]
        
        range_labels = ['<-10MW', '-10~-5MW', '-5~-1MW', '-1~1MW', '1~5MW', '5~10MW', '>10MW']
        
        distribution = []
        for i, (low, high) in enumerate(error_ranges):
            if low == -float('inf'):
                count = len(self.df[self.df['误差'] < high])
            elif high == float('inf'):
                count = len(self.df[self.df['误差'] >= low])
            else:
                count = len(self.df[(self.df['误差'] >= low) & (self.df['误差'] < high)])
            
            percentage = count / len(self.df) * 100
            distribution.append({'范围': range_labels[i], '数量': count, '百分比': percentage})
        
        dist_df = pd.DataFrame(distribution)
        print("误差分布:")
        print(dist_df)
        self.analysis_results['误差分布'] = dist_df
        
        # 正态性检验
        _, p_value = stats.normaltest(self.df['误差'])
        print(f"\n误差正态性检验 p值: {p_value:.6f}")
        print(f"误差是否符合正态分布: {'否' if p_value < 0.05 else '是'}")
        
        # 偏度和峰度
        skewness = stats.skew(self.df['误差'])
        kurtosis = stats.kurtosis(self.df['误差'])
        print(f"误差偏度: {skewness:.4f} ({'右偏' if skewness > 0 else '左偏' if skewness < 0 else '对称'})")
        print(f"误差峰度: {kurtosis:.4f} ({'尖峰' if kurtosis > 0 else '平峰' if kurtosis < 0 else '正态峰'})")
    
    def prediction_performance_analysis(self):
        """预测性能分析"""
        print("\n" + "="*60)
        print("预测性能分析")
        print("="*60)
        
        # 按真实值范围分析预测性能
        true_ranges = [(0, 10), (10, 30), (30, 60), (60, 100), (100, float('inf'))]
        range_labels = ['0-10MW', '10-30MW', '30-60MW', '60-100MW', '>100MW']
        
        performance_by_range = []
        for i, (low, high) in enumerate(true_ranges):
            if high == float('inf'):
                mask = self.df['答案'] >= low
            else:
                mask = (self.df['答案'] >= low) & (self.df['答案'] < high)
            
            subset = self.df[mask]
            if len(subset) > 0:
                performance_by_range.append({
                    '真实值范围': range_labels[i],
                    '数据点数': len(subset),
                    '平均绝对误差': subset['绝对误差'].mean(),
                    '平均相对误差(%)': subset['误差百分比'].mean(),
                    '误差标准差': subset['绝对误差'].std()
                })
        
        perf_df = pd.DataFrame(performance_by_range)
        print("按真实值范围的预测性能:")
        print(perf_df.round(4))
        self.analysis_results['按范围性能'] = perf_df
        
        # 高误差样本分析
        high_error_threshold = self.df['绝对误差'].quantile(0.9)
        high_error_samples = self.df[self.df['绝对误差'] >= high_error_threshold]
        
        print(f"\n高误差样本分析 (误差 >= {high_error_threshold:.2f}MW):")
        print(f"高误差样本数量: {len(high_error_samples)} ({len(high_error_samples)/len(self.df)*100:.2f}%)")
        print(f"高误差样本平均误差: {high_error_samples['绝对误差'].mean():.2f}MW")
        
        # 高误差样本的时间分布
        high_error_hours = high_error_samples.groupby('小时').size()
        print(f"高误差样本最集中的时间段: {high_error_hours.idxmax()}时 ({high_error_hours.max()}个样本)")
    
    def error_pattern_analysis(self):
        """误差模式识别"""
        print("\n" + "="*60)
        print("误差模式识别")
        print("="*60)
        
        # 系统性偏差分析
        mean_error = self.df['误差'].mean()
        print(f"系统性偏差: {mean_error:.4f}MW")
        print(f"偏差方向: {'高估' if mean_error < 0 else '低估' if mean_error > 0 else '无明显偏差'}")
        
        # 连续误差分析
        self.df['误差符号'] = np.sign(self.df['误差'])
        consecutive_errors = []
        current_sign = self.df['误差符号'].iloc[0]
        current_length = 1
        
        for i in range(1, len(self.df)):
            if self.df['误差符号'].iloc[i] == current_sign:
                current_length += 1
            else:
                consecutive_errors.append(current_length)
                current_sign = self.df['误差符号'].iloc[i]
                current_length = 1
        consecutive_errors.append(current_length)
        
        print(f"平均连续同向误差长度: {np.mean(consecutive_errors):.2f}")
        print(f"最长连续同向误差: {max(consecutive_errors)}")
        
        # 误差自相关分析
        error_autocorr = []
        for lag in range(1, 25):  # 分析前24个滞后期
            if lag < len(self.df):
                corr = self.df['误差'].autocorr(lag=lag)
                error_autocorr.append({'滞后期': lag, '自相关系数': corr})
        
        autocorr_df = pd.DataFrame(error_autocorr)
        significant_autocorr = autocorr_df[abs(autocorr_df['自相关系数']) > 0.1]
        
        if len(significant_autocorr) > 0:
            print(f"\n显著自相关滞后期 (|相关系数| > 0.1):")
            print(significant_autocorr.round(4))
        else:
            print(f"\n未发现显著的误差自相关模式")
    
    def generate_visualizations(self):
        """生成可视化图表"""
        print("\n" + "="*60)
        print("生成可视化图表")
        print("="*60)
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('深度误差分析可视化', fontsize=16)
        
        # 1. 误差分布直方图
        axes[0,0].hist(self.df['误差'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0,0].set_title('误差分布直方图')
        axes[0,0].set_xlabel('误差 (MW)')
        axes[0,0].set_ylabel('频次')
        axes[0,0].axvline(0, color='red', linestyle='--', alpha=0.7)
        
        # 2. 预测值vs真实值散点图
        axes[0,1].scatter(self.df['答案'], self.df['pred'], alpha=0.5, s=1)
        axes[0,1].plot([self.df['答案'].min(), self.df['答案'].max()], 
                       [self.df['答案'].min(), self.df['答案'].max()], 'r--', alpha=0.7)
        axes[0,1].set_title('预测值 vs 真实值')
        axes[0,1].set_xlabel('真实值 (MW)')
        axes[0,1].set_ylabel('预测值 (MW)')
        
        # 3. 按小时的误差箱线图
        hourly_errors = [self.df[self.df['小时']==h]['绝对误差'].values for h in range(24)]
        axes[0,2].boxplot(hourly_errors, labels=range(24))
        axes[0,2].set_title('按小时的绝对误差分布')
        axes[0,2].set_xlabel('小时')
        axes[0,2].set_ylabel('绝对误差 (MW)')
        
        # 4. 时间序列误差图
        sample_data = self.df.head(200)  # 显示前200个点
        axes[1,0].plot(sample_data.index, sample_data['误差'], alpha=0.7)
        axes[1,0].set_title('时间序列误差 (前200个点)')
        axes[1,0].set_xlabel('时间点')
        axes[1,0].set_ylabel('误差 (MW)')
        axes[1,0].axhline(0, color='red', linestyle='--', alpha=0.7)
        
        # 5. 误差vs预测值散点图
        axes[1,1].scatter(self.df['pred'], self.df['误差'], alpha=0.5, s=1)
        axes[1,1].set_title('误差 vs 预测值')
        axes[1,1].set_xlabel('预测值 (MW)')
        axes[1,1].set_ylabel('误差 (MW)')
        axes[1,1].axhline(0, color='red', linestyle='--', alpha=0.7)
        
        # 6. 累积误差分布
        sorted_errors = np.sort(np.abs(self.df['误差']))
        cumulative_pct = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors) * 100
        axes[1,2].plot(sorted_errors, cumulative_pct)
        axes[1,2].set_title('累积误差分布')
        axes[1,2].set_xlabel('绝对误差 (MW)')
        axes[1,2].set_ylabel('累积百分比 (%)')
        axes[1,2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('深度误差分析图表.png', dpi=300, bbox_inches='tight')
        print("可视化图表已保存: 深度误差分析图表.png")
        plt.close()
    
    def save_analysis_results(self):
        """保存分析结果"""
        print("\n" + "="*60)
        print("保存分析结果")
        print("="*60)

        # 保存各项分析结果到Excel
        with pd.ExcelWriter('深度误差分析结果.xlsx', engine='openpyxl') as writer:
            for sheet_name, data in self.analysis_results.items():
                if isinstance(data, pd.DataFrame):
                    # 处理多级索引问题
                    if isinstance(data.columns, pd.MultiIndex):
                        # 展平多级列名
                        data.columns = ['_'.join(col).strip() for col in data.columns.values]
                    data.to_excel(writer, sheet_name=sheet_name, index=True)

        print("分析结果已保存: 深度误差分析结果.xlsx")
    
    def run_complete_analysis(self):
        """运行完整的误差分析"""
        print("开始深度误差分析")
        print("="*60)

        if not self.load_data():
            return

        self.basic_statistics()
        self.time_dimension_analysis()
        self.error_distribution_analysis()
        self.prediction_performance_analysis()
        self.error_pattern_analysis()
        self.generate_visualizations()
        self.save_analysis_results()

        print("\n" + "="*60)
        print("深度误差分析完成")
        print("="*60)
        print("生成的文件:")
        print("- 深度误差分析结果.xlsx (详细数据)")
        print("- 深度误差分析图表.png (可视化图表)")

if __name__ == "__main__":
    analyzer = DeepErrorAnalyzer()
    analyzer.run_complete_analysis()
