#!/usr/bin/env python3
"""
特征一致性验证脚本
验证训练和预测流程使用完全相同的特征工程步骤
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入训练和预测模块
from train_and_export_model import ProductionModelPipeline
from src.data_loader import WindPowerDataLoader

def create_test_data():
    """创建测试数据"""
    test_data = {
        '时间': pd.date_range('2025-03-01', periods=50, freq='15min'),
        'wind_speed_10m': np.random.uniform(2, 15, 50),
        'wind_speed_80m': np.random.uniform(3, 18, 50),
        'wind_direction_10m': np.random.uniform(0, 360, 50),
        'wind_direction_80m': np.random.uniform(0, 360, 50),
        'wind_gusts_10m': np.random.uniform(3, 20, 50),
        'temperature_2m': np.random.uniform(-5, 25, 50),
        'rain': np.random.uniform(0, 10, 50),
        'apparent_temperature': np.random.uniform(-8, 22, 50),
        '实际功率 (MW)': np.random.uniform(10, 150, 50)  # 添加目标列用于训练
    }
    return pd.DataFrame(test_data)

def verify_feature_consistency():
    """验证特征一致性"""
    print("=" * 60)
    print("特征一致性验证")
    print("=" * 60)
    
    try:
        # 创建测试数据
        df_test = create_test_data()
        print(f"测试数据形状: {df_test.shape}")
        
        # 初始化训练管道
        pipeline = ProductionModelPipeline()
        print(f"特征工程模式: {pipeline.feature_engineering_mode}")
        
        # 1. 训练时的特征工程
        print("\n1. 训练时的特征工程...")
        df_train = df_test.copy()
        df_train_processed = pipeline.fit_feature_pipeline(df_train)
        train_features = set(df_train_processed.columns)
        print(f"   训练特征数量: {len(train_features)}")
        
        # 2. 预测时的特征工程
        print("\n2. 预测时的特征工程...")
        df_pred = df_test.drop(columns=['实际功率 (MW)']).copy()  # 移除目标列
        df_pred_processed = pipeline.transform_features(df_pred)
        pred_features = set(df_pred_processed.columns)
        print(f"   预测特征数量: {len(pred_features)}")
        
        # 3. 特征一致性检查
        print("\n3. 特征一致性检查...")
        
        # 排除目标列进行比较
        target_col = pipeline.target_column
        train_features_no_target = train_features - {target_col}
        
        missing_in_pred = train_features_no_target - pred_features
        extra_in_pred = pred_features - train_features_no_target
        
        print(f"   训练特征数量（不含目标列）: {len(train_features_no_target)}")
        print(f"   预测特征数量: {len(pred_features)}")
        print(f"   预测中缺失的特征: {len(missing_in_pred)}")
        print(f"   预测中额外的特征: {len(extra_in_pred)}")
        
        # 4. 详细分析
        if missing_in_pred:
            print(f"\n   ❌ 预测中缺失的特征 ({len(missing_in_pred)}个):")
            for i, feature in enumerate(sorted(missing_in_pred)[:10]):
                print(f"      {i+1}. {feature}")
            if len(missing_in_pred) > 10:
                print(f"      ... 还有 {len(missing_in_pred) - 10} 个")
        
        if extra_in_pred:
            print(f"\n   ⚠️ 预测中额外的特征 ({len(extra_in_pred)}个):")
            for i, feature in enumerate(sorted(extra_in_pred)[:10]):
                print(f"      {i+1}. {feature}")
            if len(extra_in_pred) > 10:
                print(f"      ... 还有 {len(extra_in_pred) - 10} 个")
        
        # 5. 特征类型分析
        print("\n4. 特征类型分析...")
        
        # 分析新增的集成特征
        integrated_features = [f for f in pred_features if any(keyword in f for keyword in [
            'huarui', 'goldwind', 'theoretical_power', 'power_coefficient', 'thrust_coefficient',
            'bias_correction', 'turbulence', 'autocorr', 'lag_', 'ma_'
        ])]
        
        print(f"   集成特征数量: {len(integrated_features)}")
        if integrated_features:
            print(f"   集成特征示例: {integrated_features[:5]}")
        
        # 6. 一致性结论
        print("\n5. 一致性结论...")
        if len(missing_in_pred) == 0 and len(extra_in_pred) == 0:
            print("   ✅ 特征完全一致！训练和预测使用相同的特征集。")
            consistency_score = 100
        else:
            total_features = len(train_features_no_target)
            consistent_features = total_features - len(missing_in_pred)
            consistency_score = (consistent_features / total_features) * 100
            print(f"   ⚠️ 特征一致性: {consistency_score:.1f}%")
            
            if len(missing_in_pred) > 0:
                print(f"   ❌ 存在 {len(missing_in_pred)} 个缺失特征，可能影响预测准确性")
            if len(extra_in_pred) > 0:
                print(f"   ⚠️ 存在 {len(extra_in_pred)} 个额外特征，不会影响预测但可能浪费计算")
        
        # 7. 保存验证报告
        print("\n6. 保存验证报告...")
        report = {
            'feature_engineering_mode': pipeline.feature_engineering_mode,
            'train_features_count': len(train_features_no_target),
            'pred_features_count': len(pred_features),
            'missing_features_count': len(missing_in_pred),
            'extra_features_count': len(extra_in_pred),
            'consistency_score': consistency_score,
            'integrated_features_count': len(integrated_features),
            'missing_features': list(missing_in_pred),
            'extra_features': list(extra_in_pred),
            'integrated_features_sample': integrated_features[:10]
        }
        
        import json
        with open('feature_consistency_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("   验证报告已保存: feature_consistency_report.json")
        
        print("\n" + "=" * 60)
        if consistency_score == 100:
            print("🎉 特征一致性验证通过！")
        else:
            print("⚠️ 特征一致性验证发现问题，请检查特征工程流程。")
        print("=" * 60)
        
        return consistency_score == 100
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_all_modes():
    """验证所有特征工程模式的一致性"""
    print("=" * 60)
    print("验证所有特征工程模式的一致性")
    print("=" * 60)
    
    modes = [1, 2, 3, 4]
    results = {}
    
    for mode in modes:
        print(f"\n{'='*40}")
        print(f"验证模式 {mode}")
        print(f"{'='*40}")
        
        try:
            # 临时修改配置
            from src.config_manager import ConfigManager
            config = ConfigManager()
            original_mode = config.get('feature_engineering.mode', 4)
            config.set('feature_engineering.mode', mode)
            
            # 验证该模式
            success = verify_feature_consistency()
            results[mode] = success
            
            # 恢复原始配置
            config.set('feature_engineering.mode', original_mode)
            
        except Exception as e:
            print(f"❌ 模式 {mode} 验证失败: {e}")
            results[mode] = False
    
    # 总结
    print(f"\n{'='*60}")
    print("所有模式验证结果")
    print(f"{'='*60}")
    
    for mode, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"模式 {mode}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有模式的特征一致性验证都通过了！")
    else:
        print("\n⚠️ 部分模式存在特征一致性问题，请检查相关代码。")
    
    return all_passed

if __name__ == "__main__":
    # 验证当前配置的模式
    print("验证当前配置的特征工程模式...")
    current_success = verify_feature_consistency()
    
    # 询问是否验证所有模式
    if current_success:
        try:
            verify_all = input("\n是否验证所有特征工程模式？(y/n): ").strip().lower()
            if verify_all in ['y', 'yes']:
                verify_all_modes()
        except KeyboardInterrupt:
            print("\n用户取消操作")
    
    print("\n验证完成！")
