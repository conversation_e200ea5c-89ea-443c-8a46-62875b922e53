"""
一键智能修复系统
自动检测和修复所有常见问题，无需手动选择
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from pathlib import Path
import json
import joblib
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# 导入配置管理器
import sys
sys.path.append(str(Path(__file__).parent / "src"))
from config_manager import ConfigManager

class AutoFixer:
    """
    自动修复器 - 智能检测和修复所有问题
    """
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        self.lightgbm_version = None
        self.config = ConfigManager()
        self._check_lightgbm_version()
        
    def log_issue(self, issue):
        """记录发现的问题"""
        self.issues_found.append(issue)
        print(f"🔍 发现问题: {issue}")
    
    def log_fix(self, fix):
        """记录应用的修复"""
        self.fixes_applied.append(fix)
        print(f"✅ 已修复: {fix}")

    def _check_lightgbm_version(self):
        """检查LightGBM版本"""
        try:
            self.lightgbm_version = lgb.__version__
            print(f"LightGBM版本: {self.lightgbm_version}")
        except:
            self.lightgbm_version = "unknown"

    def _train_lgb_compatible(self, params, train_data, **kwargs):
        """兼容不同版本的LightGBM训练"""
        try:
            # 尝试新版本方式 (callbacks)
            return lgb.train(params, train_data,
                           callbacks=[lgb.log_evaluation(0)], **kwargs)
        except TypeError:
            # 回退到旧版本方式
            return lgb.train(params, train_data, verbose_eval=False, **kwargs)
    
    def check_data_files(self):
        """检查数据文件"""
        print("检查数据文件...")

        # 从配置获取数据目录和文件名
        data_dir = Path(self.config.get('data_files.data_directory', 'data'))
        train_file = self.config.get('data_files.train_file')
        test_file = self.config.get('data_files.test_file')

        if not data_dir.exists():
            self.log_issue(f"数据目录不存在: {data_dir}")
            return False

        # 检查配置的文件是否存在
        train_path = data_dir / train_file
        test_path = data_dir / test_file

        if not train_path.exists():
            self.log_issue(f"配置的训练文件不存在: {train_file}")
            # 尝试自动查找
            train_files = list(data_dir.glob("*训练*.csv")) + list(data_dir.glob("*train*.csv"))
            if train_files:
                print(f"  建议更新配置，可用文件: {[f.name for f in train_files]}")
            return False

        if not test_path.exists():
            self.log_issue(f"配置的测试文件不存在: {test_file}")
            # 尝试自动查找
            test_files = list(data_dir.glob("*测试*.csv")) + list(data_dir.glob("*test*.csv"))
            if test_files:
                print(f"  建议更新配置，可用文件: {[f.name for f in test_files]}")
            return False

        print(f"✅ 训练文件: {train_file}")
        print(f"✅ 测试文件: {test_file}")
        return True
    
    def fix_encoding_issues(self, file_path):
        """修复编码问题"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return df, encoding
            except UnicodeDecodeError:
                continue
        
        self.log_issue(f"无法读取文件: {file_path}")
        return None, None
    
    def fix_data_types(self, df, exclude_cols=None):
        """修复数据类型问题"""
        if exclude_cols is None:
            exclude_cols = ['时间']
        
        df_fixed = df.copy()
        type_issues = 0
        
        for col in df_fixed.columns:
            if col in exclude_cols:
                continue
            
            original_dtype = df_fixed[col].dtype
            
            # 修复object类型
            if original_dtype == 'object':
                type_issues += 1
                try:
                    df_fixed[col] = pd.to_numeric(df_fixed[col], errors='coerce')
                    if df_fixed[col].isnull().all():
                        le = LabelEncoder()
                        df_fixed[col] = le.fit_transform(df_fixed[col].astype(str).fillna('unknown'))
                except:
                    df_fixed[col] = 0
            
            # 修复category类型
            elif original_dtype.name == 'category':
                type_issues += 1
                try:
                    df_fixed[col] = pd.to_numeric(df_fixed[col], errors='coerce')
                    if df_fixed[col].isnull().all():
                        le = LabelEncoder()
                        df_fixed[col] = le.fit_transform(df_fixed[col].astype(str).fillna('unknown'))
                except:
                    df_fixed[col] = 0
        
        if type_issues > 0:
            self.log_fix(f"修复了 {type_issues} 个数据类型问题")
        
        return df_fixed
    
    def fix_missing_values(self, df, exclude_cols=None):
        """修复缺失值"""
        if exclude_cols is None:
            exclude_cols = ['时间']
        
        df_fixed = df.copy()
        missing_issues = 0
        
        for col in df_fixed.columns:
            if col in exclude_cols:
                continue
            
            if df_fixed[col].isnull().any():
                missing_issues += 1
                if df_fixed[col].dtype in ['float64', 'int64', 'float32', 'int32']:
                    df_fixed[col] = df_fixed[col].fillna(df_fixed[col].median())
                else:
                    df_fixed[col] = df_fixed[col].fillna(0)
        
        if missing_issues > 0:
            self.log_fix(f"修复了 {missing_issues} 个缺失值问题")
        
        return df_fixed
    
    def test_lightgbm_compatibility(self, X, y=None):
        """测试LightGBM兼容性"""
        try:
            if y is not None:
                lgb_data = lgb.Dataset(X, label=y)
            else:
                dummy_y = np.zeros(len(X))
                lgb_data = lgb.Dataset(X, label=dummy_y)
            
            # 尝试训练简单模型
            if y is not None:
                params = {'objective': 'regression', 'verbose': -1, 'num_leaves': 10}
                model = self._train_lgb_compatible(params, lgb_data, num_boost_round=5)
                model.predict(X)
            
            return True, None
        except Exception as e:
            return False, str(e)
    
    def auto_fix_all(self):
        """自动修复所有问题"""
        print("=" * 60)
        print("🤖 智能自动修复系统")
        print("=" * 60)
        print("正在自动检测和修复所有问题...")
        
        # 1. 检查数据文件
        if not self.check_data_files():
            print("❌ 数据文件检查失败，请确保数据文件在data目录中")
            return False
        
        # 2. 检查是否已有处理后的数据
        processed_dir = Path("processed_data")
        train_processed = processed_dir / "train_with_features.csv"
        test_processed = processed_dir / "test_with_features.csv"
        
        if train_processed.exists():
            print("\n检查已处理的数据...")
            
            # 加载处理后的数据
            train_df, _ = self.fix_encoding_issues(train_processed)
            if train_df is None:
                self.log_issue("无法读取处理后的训练数据")
                return False
            
            # 检查和修复数据类型
            original_shape = train_df.shape
            train_df = self.fix_data_types(train_df, ['时间', '理论功率 (MW)'])
            train_df = self.fix_missing_values(train_df, ['时间', '理论功率 (MW)'])
            
            # 测试LightGBM兼容性
            feature_cols = [col for col in train_df.columns if col not in ['时间', '理论功率 (MW)']]
            X_train = train_df[feature_cols]
            y_train = train_df['理论功率 (MW)'] if '理论功率 (MW)' in train_df.columns else None
            
            compatible, error = self.test_lightgbm_compatibility(X_train, y_train)
            
            if not compatible:
                self.log_issue(f"LightGBM兼容性问题: {error}")
                # 进一步修复
                X_train = self.fix_data_types(X_train)
                X_train = self.fix_missing_values(X_train)
                train_df[feature_cols] = X_train
                
                # 重新测试
                compatible, error = self.test_lightgbm_compatibility(X_train, y_train)
                if compatible:
                    self.log_fix("LightGBM兼容性问题已解决")
            
            # 保存修复后的数据
            if self.fixes_applied:
                # 备份原文件
                backup_path = processed_dir / "train_with_features_backup_auto.csv"
                if not backup_path.exists():
                    pd.read_csv(train_processed).to_csv(backup_path, index=False)
                    print(f"✅ 原数据已备份: {backup_path}")
                
                # 保存修复后的数据
                train_df.to_csv(train_processed, index=False)
                self.log_fix("训练数据已更新")
                
                # 同样处理测试数据
                if test_processed.exists():
                    test_df, _ = self.fix_encoding_issues(test_processed)
                    if test_df is not None:
                        test_df = self.fix_data_types(test_df, ['时间'])
                        test_df = self.fix_missing_values(test_df, ['时间'])
                        test_df.to_csv(test_processed, index=False)
                        self.log_fix("测试数据已更新")
        
        else:
            print("\n未找到处理后的数据，建议运行特征工程")
            self.log_issue("需要先运行特征工程")
        
        # 3. 检查生产模型
        model_dir = Path("production_model")
        if model_dir.exists():
            config_file = model_dir / "latest_config.json"
            if config_file.exists():
                print("\n检查生产模型...")
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    model_file = model_dir / config['model_file']
                    model = joblib.load(model_file)
                    print(f"✅ 生产模型正常: {config['timestamp']}")
                except Exception as e:
                    self.log_issue(f"生产模型问题: {e}")
        
        # 4. 总结
        print(f"\n" + "=" * 60)
        print("🎯 自动修复完成")
        print("=" * 60)
        
        if self.issues_found:
            print(f"发现的问题 ({len(self.issues_found)}):")
            for issue in self.issues_found:
                print(f"  ⚠️  {issue}")
        
        if self.fixes_applied:
            print(f"\n应用的修复 ({len(self.fixes_applied)}):")
            for fix in self.fixes_applied:
                print(f"  ✅ {fix}")
        
        if not self.issues_found and not self.fixes_applied:
            print("🎉 系统状态良好，无需修复！")
        
        # 5. 建议下一步
        print(f"\n📋 建议下一步:")
        if not train_processed.exists():
            print("1. 运行特征工程: python 02_feature_engineering.py")
            print("2. 训练生产模型: python train_and_export_model.py")
        elif not model_dir.exists():
            print("1. 训练生产模型: python train_and_export_model.py")
        else:
            print("1. 系统已就绪，可以进行每日预测: python predict_today.py")
            print("2. 下载最新天气预报: python download_weather_forecast.py")
        
        return len(self.issues_found) == 0

def main():
    """
    主修复流程
    """
    fixer = AutoFixer()
    success = fixer.auto_fix_all()
    
    if success:
        print(f"\n🎉 所有问题已自动解决！")
    else:
        print(f"\n⚠️  发现一些问题，请查看上述建议")

if __name__ == "__main__":
    main()
