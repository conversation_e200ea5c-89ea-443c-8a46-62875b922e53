"""
测试多源数据合并修复
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager
from src.multi_source_wind_fusion import MultiSourceWindFusion

def test_merge_fix():
    """测试合并修复"""
    print("=" * 60)
    print("测试多源数据合并修复")
    print("=" * 60)
    
    try:
        # 初始化配置管理器
        config = ConfigManager()
        
        # 创建多源风速融合器
        fusion = MultiSourceWindFusion(config)
        
        # 获取多源数据配置
        multi_source_config = config.get('multi_source_wind', {})
        if not multi_source_config.get('enable', False):
            print("多源风速数据功能未启用")
            return False
            
        train_data_dir = config.get('data_files.train_data_directory', 'data/training')
        train_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
        train_path = Path(train_data_dir) / train_file
        
        if not train_path.exists():
            print(f"数据文件不存在: {train_path}")
            return False
        
        print(f"加载数据: {train_path}")
        
        # 加载多源数据（取前50行测试）
        multi_source_data = fusion.load_multi_source_data(str(train_path))
        multi_source_sample = multi_source_data.head(50).copy()
        
        print(f"多源数据形状: {multi_source_sample.shape}")
        print(f"时间列类型: {multi_source_sample['时间'].dtype}")
        print(f"时间范围: {multi_source_sample['时间'].min()} 到 {multi_source_sample['时间'].max()}")
        
        # 执行特征融合
        print("\n执行特征融合...")
        features = fusion.fuse_multi_source_data(multi_source_sample)
        
        print(f"融合特征形状: {features.shape}")
        print(f"特征索引类型: {features.index.dtype}")
        print(f"特征索引范围: {features.index.min()} 到 {features.index.max()}")
        
        # 创建模拟主数据集
        print("\n创建模拟主数据集...")
        main_data = pd.DataFrame({
            '时间': multi_source_sample['时间'].copy(),
            'wind_speed_10m': np.random.uniform(3, 15, len(multi_source_sample)),
            'wind_speed_80m': np.random.uniform(4, 18, len(multi_source_sample)),
            'temperature_2m': np.random.uniform(-5, 25, len(multi_source_sample)),
        })
        
        print(f"主数据形状: {main_data.shape}")
        print(f"主数据时间列类型: {main_data['时间'].dtype}")
        
        # 测试合并逻辑
        print("\n测试合并逻辑...")
        
        # 确保时间列格式一致
        main_data['时间'] = pd.to_datetime(main_data['时间'])
        features.index = pd.to_datetime(features.index)
        
        print(f"转换后主数据时间类型: {main_data['时间'].dtype}")
        print(f"转换后特征索引类型: {features.index.dtype}")
        
        # 使用时间索引进行合并
        main_data_indexed = main_data.set_index('时间')
        merged_data = pd.concat([main_data_indexed, features], axis=1, join='inner')
        merged_data = merged_data.reset_index()
        
        print(f"合并后数据形状: {merged_data.shape}")
        print(f"合并后列数: {len(merged_data.columns)}")
        print(f"原主数据列数: {len(main_data.columns)}")
        print(f"特征列数: {len(features.columns)}")
        print(f"新增特征数: {len(merged_data.columns) - len(main_data.columns)}")
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        print(f"缺失值数量: {merged_data.isnull().sum().sum()}")
        print(f"无穷值数量: {np.isinf(merged_data.select_dtypes(include=[np.number]).values).sum()}")
        
        print("\n✅ 合并测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_merge_fix()
    
    if success:
        print("\n" + "=" * 60)
        print("SUCCESS: 多源数据合并修复验证通过!")
        print("现在可以正常运行训练脚本了")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("FAILED: 合并修复验证失败")
        print("需要进一步调试")
        print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
