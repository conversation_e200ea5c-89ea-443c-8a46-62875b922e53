"""
多源风速数据集成测试脚本
验证多源风速数据的加载、融合和特征工程功能
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加src路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.config_manager import ConfigManager
from src.multi_source_wind_fusion import MultiSourceWindFusion
from src.comprehensive_feature_engine import ComprehensiveFeatureEngine

def test_multi_source_data_loading():
    """测试多源风速数据加载"""
    print("=" * 60)
    print("测试1: 多源风速数据加载")
    print("=" * 60)
    
    try:
        # 初始化配置管理器
        config = ConfigManager()
        
        # 创建多源风速融合器
        fusion = MultiSourceWindFusion(config)
        
        # 获取训练数据路径
        multi_source_config = config.get('multi_source_wind', {})
        if not multi_source_config.get('enable', False):
            print("❌ 多源风速数据功能未启用")
            return False
            
        train_data_dir = config.get('data_files.train_data_directory', 'data/training')
        train_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
        train_path = Path(train_data_dir) / train_file
        
        if not train_path.exists():
            print(f"❌ 训练数据文件不存在: {train_path}")
            return False
        
        # 加载数据
        print(f"正在加载: {train_path}")
        df = fusion.load_multi_source_data(str(train_path))
        
        print(f"✅ 数据加载成功")
        print(f"   数据形状: {df.shape}")
        print(f"   时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
        print(f"   列数: {len(df.columns)}")
        
        # 识别风速列
        wind_columns = fusion.identify_wind_columns(df)
        print(f"   10m风速列: {len(wind_columns['wind_10m'])}")
        print(f"   80m风速列: {len(wind_columns['wind_80m'])}")
        print(f"   阵风列: {len(wind_columns['gusts_10m'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_source_feature_fusion():
    """测试多源风速特征融合"""
    print("\n" + "=" * 60)
    print("测试2: 多源风速特征融合")
    print("=" * 60)
    
    try:
        # 初始化配置管理器
        config = ConfigManager()
        fusion = MultiSourceWindFusion(config)
        
        # 加载数据
        multi_source_config = config.get('multi_source_wind', {})
        train_data_dir = config.get('data_files.train_data_directory', 'data/training')
        train_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
        train_path = Path(train_data_dir) / train_file
        
        df = fusion.load_multi_source_data(str(train_path))
        
        # 取前100行进行测试（加快速度）
        df_sample = df.head(100).copy()
        
        print(f"使用样本数据进行测试，形状: {df_sample.shape}")
        
        # 执行特征融合
        features = fusion.fuse_multi_source_data(df_sample)
        
        print(f"✅ 特征融合成功")
        print(f"   生成特征数: {len(features.columns)}")
        print(f"   特征形状: {features.shape}")
        
        # 显示特征分组
        feature_groups = fusion.get_feature_importance_groups()
        print(f"   特征分组数: {len(feature_groups)}")
        for group_name, group_features in feature_groups.items():
            available_features = [f for f in group_features if f in features.columns]
            print(f"     {group_name}: {len(available_features)} 个特征")
        
        # 检查数据质量
        print(f"   无穷值数量: {np.isinf(features.values).sum()}")
        print(f"   NaN值数量: {features.isna().sum().sum()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征融合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_feature_integration():
    """测试综合特征工程集成"""
    print("\n" + "=" * 60)
    print("测试3: 综合特征工程集成")
    print("=" * 60)
    
    try:
        # 初始化配置管理器
        config = ConfigManager()
        
        # 创建综合特征工程引擎
        feature_engine = ComprehensiveFeatureEngine(config)
        
        # 加载主数据（模拟）
        main_data = pd.DataFrame({
            '时间': pd.date_range('2024-03-01', periods=50, freq='15min'),
            'wind_speed_10m': np.random.uniform(3, 15, 50),
            'wind_speed_80m': np.random.uniform(4, 18, 50),
            'wind_direction_10m': np.random.uniform(0, 360, 50),
            'wind_direction_80m': np.random.uniform(0, 360, 50),
            'wind_gusts_10m': np.random.uniform(5, 20, 50),
            'temperature_2m': np.random.uniform(-5, 25, 50),
            'rain': np.random.uniform(0, 5, 50),
            'apparent_temperature': np.random.uniform(-8, 28, 50)
        })
        
        # 加载多源风速数据
        multi_source_config = config.get('multi_source_wind', {})
        if multi_source_config.get('enable', False):
            train_data_dir = config.get('data_files.train_data_directory', 'data/training')
            train_file = multi_source_config.get('train_file', '站点c_多网站历史风速20250228之前.csv')
            train_path = Path(train_data_dir) / train_file
            
            if train_path.exists():
                multi_source_data = feature_engine.multi_source_fusion.load_multi_source_data(str(train_path))
                # 取对应时间段的数据
                multi_source_data = multi_source_data.head(50).copy()
                multi_source_data['时间'] = main_data['时间']  # 确保时间对齐
            else:
                print("⚠️  多源风速数据文件不存在，跳过多源特征")
                multi_source_data = None
        else:
            print("⚠️  多源风速数据功能未启用")
            multi_source_data = None
        
        print(f"主数据形状: {main_data.shape}")
        if multi_source_data is not None:
            print(f"多源数据形状: {multi_source_data.shape}")
        
        # 执行综合特征工程
        result = feature_engine.process_comprehensive_features(
            main_data,
            multi_source_data=multi_source_data,
            enable_data_quality=True,
            enable_physics=True,
            enable_time=True,
            enable_breakthrough=True,
            enable_multi_source=True
        )
        
        print(f"✅ 综合特征工程成功")
        print(f"   最终特征数: {len(result.columns)}")
        print(f"   数据形状: {result.shape}")
        
        # 显示特征统计
        if hasattr(feature_engine, 'feature_stats'):
            print("   特征统计:")
            for category, stats in feature_engine.feature_stats.items():
                print(f"     {category}: {stats['new_features']} 个新特征")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合特征工程集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_consistency():
    """测试特征一致性"""
    print("\n" + "=" * 60)
    print("测试4: 特征一致性验证")
    print("=" * 60)
    
    try:
        config = ConfigManager()
        
        # 检查配置文件
        multi_source_config = config.get('multi_source_wind', {})
        print(f"多源风速配置: {multi_source_config.get('enable', False)}")
        
        if multi_source_config.get('enable', False):
            train_file = multi_source_config.get('train_file')
            test_file = multi_source_config.get('test_file')
            print(f"训练文件: {train_file}")
            print(f"测试文件: {test_file}")
            
            # 检查文件存在性
            train_data_dir = config.get('data_files.train_data_directory', 'data/training')
            test_data_dir = config.get('data_files.test_data_directory', 'data/testing')
            
            train_path = Path(train_data_dir) / train_file
            test_path = Path(test_data_dir) / test_file
            
            print(f"训练文件存在: {train_path.exists()}")
            print(f"测试文件存在: {test_path.exists()}")
            
            if train_path.exists() and test_path.exists():
                # 检查列一致性
                fusion = MultiSourceWindFusion(config)
                train_df = fusion.load_multi_source_data(str(train_path))
                test_df = fusion.load_multi_source_data(str(test_path))
                
                train_cols = set(train_df.columns)
                test_cols = set(test_df.columns)
                
                print(f"训练集列数: {len(train_cols)}")
                print(f"测试集列数: {len(test_cols)}")
                print(f"列一致性: {train_cols == test_cols}")
                
                if train_cols != test_cols:
                    missing_in_test = train_cols - test_cols
                    missing_in_train = test_cols - train_cols
                    if missing_in_test:
                        print(f"测试集缺少的列: {missing_in_test}")
                    if missing_in_train:
                        print(f"训练集缺少的列: {missing_in_train}")
        
        print("✅ 特征一致性验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 特征一致性验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 多源风速数据集成测试")
    print("=" * 60)
    
    tests = [
        test_multi_source_data_loading,
        test_multi_source_feature_fusion,
        test_comprehensive_feature_integration,
        test_feature_consistency
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！多源风速数据集成功能正常")
    else:
        print("⚠️  部分测试失败，请检查配置和数据文件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
