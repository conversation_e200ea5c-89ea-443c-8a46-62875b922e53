#!/usr/bin/env python3
"""
集成特征工程一致性验证脚本
专门验证新增的风机参数集成特征工程是否在训练和预测中保持一致
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from src.integrated_feature_engineer import IntegratedFeatureEngineer

def create_test_data():
    """创建测试数据"""
    test_data = {
        '时间': pd.date_range('2025-03-01', periods=20, freq='15min'),
        'wind_speed_10m': np.random.uniform(2, 15, 20),
        'wind_speed_80m': np.random.uniform(3, 18, 20),
        'wind_direction_10m': np.random.uniform(0, 360, 20),
        'wind_direction_80m': np.random.uniform(0, 360, 20),
        'wind_gusts_10m': np.random.uniform(3, 20, 20),
        'temperature_2m': np.random.uniform(-5, 25, 20),
        'rain': np.random.uniform(0, 10, 20),
        'apparent_temperature': np.random.uniform(-8, 22, 20)
    }
    return pd.DataFrame(test_data)

def verify_integrated_feature_consistency():
    """验证集成特征工程的一致性"""
    print("=" * 60)
    print("集成特征工程一致性验证")
    print("=" * 60)
    
    try:
        # 创建相同的测试数据
        df_test = create_test_data()
        print(f"测试数据形状: {df_test.shape}")
        
        # 初始化集成特征工程器
        engineer = IntegratedFeatureEngineer()
        print(f"启用的模块: {engineer._get_enabled_modules()}")
        
        # 1. 训练模式的特征工程
        print("\n1. 训练模式的特征工程...")
        df_train = df_test.copy()
        df_train_processed = engineer.engineer_features(df_train, is_training=True)
        train_features = set(df_train_processed.columns)
        print(f"   训练模式特征数量: {len(train_features)}")
        
        # 2. 预测模式的特征工程
        print("\n2. 预测模式的特征工程...")
        df_pred = df_test.copy()
        df_pred_processed = engineer.engineer_features(df_pred, is_training=False)
        pred_features = set(df_pred_processed.columns)
        print(f"   预测模式特征数量: {len(pred_features)}")
        
        # 3. 特征一致性检查
        print("\n3. 特征一致性检查...")
        
        missing_in_pred = train_features - pred_features
        extra_in_pred = pred_features - train_features
        
        print(f"   预测中缺失的特征: {len(missing_in_pred)}")
        print(f"   预测中额外的特征: {len(extra_in_pred)}")
        
        # 4. 详细分析
        if missing_in_pred:
            print(f"\n   ❌ 预测中缺失的特征:")
            for feature in sorted(missing_in_pred):
                print(f"      - {feature}")
        
        if extra_in_pred:
            print(f"\n   ⚠️ 预测中额外的特征:")
            for feature in sorted(extra_in_pred):
                print(f"      - {feature}")
        
        # 5. 分析新增的集成特征
        print("\n4. 集成特征分析...")
        
        # 风机参数特征
        turbine_features = [f for f in pred_features if any(keyword in f for keyword in [
            'huarui', 'goldwind', 'theoretical_power', 'power_coefficient', 'thrust_coefficient'
        ])]
        
        # 物理约束特征
        physics_features = [f for f in pred_features if any(keyword in f for keyword in [
            'bias_correction', 'density_corrected', 'turbulence', 'effectiveness'
        ])]
        
        # 时序增强特征
        temporal_features = [f for f in pred_features if any(keyword in f for keyword in [
            'lag_', 'ma_', 'autocorr', 'hour_', 'time_', 'stability'
        ])]
        
        print(f"   风机参数特征: {len(turbine_features)}个")
        print(f"   物理约束特征: {len(physics_features)}个")
        print(f"   时序增强特征: {len(temporal_features)}个")
        
        if turbine_features:
            print(f"   风机参数特征示例: {turbine_features[:3]}")
        if physics_features:
            print(f"   物理约束特征示例: {physics_features[:3]}")
        if temporal_features:
            print(f"   时序增强特征示例: {temporal_features[:3]}")
        
        # 6. 数值一致性检查
        print("\n5. 数值一致性检查...")
        
        # 检查相同特征的数值是否一致
        common_features = train_features & pred_features
        numerical_differences = []
        
        for feature in common_features:
            if feature != '时间':  # 跳过时间列
                train_values = df_train_processed[feature].values
                pred_values = df_pred_processed[feature].values
                
                # 检查数值差异
                if not np.allclose(train_values, pred_values, rtol=1e-10, atol=1e-10):
                    max_diff = np.max(np.abs(train_values - pred_values))
                    numerical_differences.append((feature, max_diff))
        
        if numerical_differences:
            print(f"   ⚠️ 发现 {len(numerical_differences)} 个特征的数值不一致:")
            for feature, diff in sorted(numerical_differences, key=lambda x: x[1], reverse=True)[:5]:
                print(f"      {feature}: 最大差异 {diff:.2e}")
        else:
            print(f"   ✅ 所有共同特征的数值完全一致")
        
        # 7. 一致性结论
        print("\n6. 一致性结论...")
        
        feature_consistency = len(missing_in_pred) == 0 and len(extra_in_pred) == 0
        numerical_consistency = len(numerical_differences) == 0
        
        if feature_consistency and numerical_consistency:
            print("   ✅ 集成特征工程完全一致！")
            print("   ✅ 训练和预测使用相同的特征集和计算逻辑")
            consistency_score = 100
        else:
            issues = []
            if not feature_consistency:
                issues.append("特征集不一致")
            if not numerical_consistency:
                issues.append("数值计算不一致")
            
            print(f"   ❌ 发现问题: {', '.join(issues)}")
            consistency_score = 0 if not feature_consistency else 50
        
        # 8. 保存验证报告
        print("\n7. 保存验证报告...")
        
        report = {
            'feature_consistency': feature_consistency,
            'numerical_consistency': numerical_consistency,
            'consistency_score': consistency_score,
            'train_features_count': len(train_features),
            'pred_features_count': len(pred_features),
            'missing_features_count': len(missing_in_pred),
            'extra_features_count': len(extra_in_pred),
            'numerical_differences_count': len(numerical_differences),
            'turbine_features_count': len(turbine_features),
            'physics_features_count': len(physics_features),
            'temporal_features_count': len(temporal_features),
            'enabled_modules': engineer._get_enabled_modules(),
            'missing_features': list(missing_in_pred),
            'extra_features': list(extra_in_pred),
            'turbine_features_sample': turbine_features[:10],
            'physics_features_sample': physics_features[:10],
            'temporal_features_sample': temporal_features[:10]
        }
        
        import json
        with open('integrated_feature_consistency_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("   验证报告已保存: integrated_feature_consistency_report.json")
        
        print("\n" + "=" * 60)
        if consistency_score == 100:
            print("🎉 集成特征工程一致性验证通过！")
            print("✅ 新的风机参数特征已正确集成到训练和预测流程中")
        else:
            print("⚠️ 集成特征工程一致性验证发现问题")
        print("=" * 60)
        
        return consistency_score == 100
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_generation():
    """测试特征生成效果"""
    print("\n" + "=" * 60)
    print("测试特征生成效果")
    print("=" * 60)
    
    try:
        # 创建测试数据
        df_test = create_test_data()
        print(f"原始数据形状: {df_test.shape}")
        
        # 应用集成特征工程
        engineer = IntegratedFeatureEngineer()
        df_enhanced = engineer.engineer_features(df_test, is_training=True)
        
        print(f"增强后数据形状: {df_enhanced.shape}")
        print(f"新增特征数量: {df_enhanced.shape[1] - df_test.shape[1]}")
        
        # 获取特征重要性排序
        ranking = engineer.get_feature_importance_ranking()
        
        print(f"\n特征重要性分组:")
        for priority, features in ranking.items():
            if features:
                print(f"  {priority}: {len(features)}个特征")
        
        # 检查关键特征是否存在
        key_features = [
            'total_theoretical_power',
            'final_corrected_theoretical_power', 
            'huarui_theoretical_power',
            'goldwind_theoretical_power',
            'systematic_bias_correction',
            'hour_error_intensity'
        ]
        
        print(f"\n关键特征检查:")
        for feature in key_features:
            exists = feature in df_enhanced.columns
            status = "✅" if exists else "❌"
            print(f"  {status} {feature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征生成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始集成特征工程验证...")
    
    # 1. 验证特征一致性
    consistency_ok = verify_integrated_feature_consistency()
    
    # 2. 测试特征生成效果
    generation_ok = test_feature_generation()
    
    # 总结
    print(f"\n{'='*60}")
    print("验证总结")
    print(f"{'='*60}")
    print(f"特征一致性: {'✅ 通过' if consistency_ok else '❌ 失败'}")
    print(f"特征生成: {'✅ 通过' if generation_ok else '❌ 失败'}")
    
    if consistency_ok and generation_ok:
        print(f"\n🎉 集成特征工程系统验证通过！")
        print(f"✅ 新的风机参数特征已成功集成")
        print(f"✅ 训练和预测流程特征完全一致")
        print(f"✅ 系统已准备好进行准确率提升测试")
    else:
        print(f"\n⚠️ 验证发现问题，请检查系统配置")
    
    print(f"\n验证完成！")
