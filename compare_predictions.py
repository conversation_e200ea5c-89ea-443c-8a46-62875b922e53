"""
比较预测结果，验证多源风速数据的影响
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def compare_recent_predictions():
    """比较最近的预测结果"""
    print("=" * 60)
    print("预测结果比较分析")
    print("=" * 60)
    
    # 获取预测文件
    prediction_dir = Path("daily_predictions")
    prediction_files = sorted(prediction_dir.glob("今日预测_*.csv"))
    
    if len(prediction_files) < 5:
        print("ERROR: 预测文件不足")
        return False
    
    # 取最近的5个预测文件
    recent_files = prediction_files[-5:]
    
    print("分析最近5个预测文件:")
    for i, file in enumerate(recent_files, 1):
        print(f"  {i}. {file.name}")
    
    # 加载预测数据
    predictions = []
    for file in recent_files:
        try:
            df = pd.read_csv(file)
            if 'pred' in df.columns:
                predictions.append(df['pred'].values)
            else:
                print(f"WARNING: {file.name} 缺少pred列")
                continue
        except Exception as e:
            print(f"ERROR: 无法加载 {file.name}: {e}")
            continue
    
    if len(predictions) < 2:
        print("ERROR: 有效预测文件不足")
        return False
    
    # 分析预测差异
    print(f"\n预测差异分析 (基于 {len(predictions)} 个文件):")
    
    # 计算相邻预测的差异
    differences = []
    for i in range(1, len(predictions)):
        diff = np.abs(predictions[i] - predictions[i-1])
        differences.append(diff)
        
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        std_diff = np.std(diff)
        
        print(f"  预测 {i+1} vs 预测 {i}:")
        print(f"    最大差异: {max_diff:.3f} MW")
        print(f"    平均差异: {mean_diff:.3f} MW")
        print(f"    标准差: {std_diff:.3f} MW")
        
        # 判断是否有显著差异
        if max_diff > 0.1:  # 大于0.1MW的差异认为是显著的
            print(f"    状态: 有显著差异 ✓")
        else:
            print(f"    状态: 差异很小 ⚠")
    
    # 整体统计
    all_diffs = np.concatenate(differences)
    print(f"\n整体差异统计:")
    print(f"  总体最大差异: {np.max(all_diffs):.3f} MW")
    print(f"  总体平均差异: {np.mean(all_diffs):.3f} MW")
    print(f"  总体标准差: {np.std(all_diffs):.3f} MW")
    
    # 分析预测值范围
    all_preds = np.concatenate(predictions)
    print(f"\n预测值范围分析:")
    print(f"  最小值: {np.min(all_preds):.3f} MW")
    print(f"  最大值: {np.max(all_preds):.3f} MW")
    print(f"  平均值: {np.mean(all_preds):.3f} MW")
    print(f"  标准差: {np.std(all_preds):.3f} MW")
    
    # 判断多源数据是否起作用
    significant_changes = np.sum(all_diffs > 0.1)
    total_comparisons = len(all_diffs)
    change_ratio = significant_changes / total_comparisons
    
    print(f"\n多源数据影响评估:")
    print(f"  显著变化点数: {significant_changes}")
    print(f"  总比较点数: {total_comparisons}")
    print(f"  变化比例: {change_ratio:.1%}")
    
    if change_ratio > 0.1:  # 超过10%的点有显著变化
        print(f"  结论: 多源风速数据正在起作用 ✓")
        return True
    else:
        print(f"  结论: 多源风速数据影响较小 ⚠")
        return False

def analyze_prediction_patterns():
    """分析预测模式"""
    print("\n" + "=" * 60)
    print("预测模式分析")
    print("=" * 60)
    
    # 获取最新的预测文件
    prediction_dir = Path("daily_predictions")
    prediction_files = sorted(prediction_dir.glob("今日预测_*.csv"))
    
    if len(prediction_files) == 0:
        print("ERROR: 没有预测文件")
        return False
    
    latest_file = prediction_files[-1]
    print(f"分析文件: {latest_file.name}")
    
    try:
        df = pd.read_csv(latest_file)
        
        if 'pred' not in df.columns:
            print("ERROR: 预测文件缺少pred列")
            return False
        
        predictions = df['pred'].values
        
        print(f"预测数据点数: {len(predictions)}")
        print(f"预测时间跨度: {len(predictions) * 15 / 60:.1f} 小时")
        
        # 基本统计
        print(f"\n基本统计:")
        print(f"  最小值: {np.min(predictions):.3f} MW")
        print(f"  最大值: {np.max(predictions):.3f} MW")
        print(f"  平均值: {np.mean(predictions):.3f} MW")
        print(f"  中位数: {np.median(predictions):.3f} MW")
        print(f"  标准差: {np.std(predictions):.3f} MW")
        
        # 变化分析
        changes = np.diff(predictions)
        print(f"\n变化分析:")
        print(f"  最大增幅: {np.max(changes):.3f} MW")
        print(f"  最大降幅: {np.min(changes):.3f} MW")
        print(f"  平均变化: {np.mean(np.abs(changes)):.3f} MW")
        
        # 功率区间分析
        low_power = np.sum(predictions < 10)
        medium_power = np.sum((predictions >= 10) & (predictions < 50))
        high_power = np.sum(predictions >= 50)
        
        print(f"\n功率区间分析:")
        print(f"  低功率 (<10MW): {low_power} 点 ({low_power/len(predictions):.1%})")
        print(f"  中功率 (10-50MW): {medium_power} 点 ({medium_power/len(predictions):.1%})")
        print(f"  高功率 (>50MW): {high_power} 点 ({high_power/len(predictions):.1%})")
        
        # 检查是否有合理的变化模式
        if np.std(predictions) > 5 and np.mean(np.abs(changes)) > 1:
            print(f"\n✓ 预测显示合理的功率变化模式")
            print(f"✓ 多源风速数据可能正在提供有价值的信息")
            return True
        else:
            print(f"\n⚠ 预测变化较小，可能需要检查数据质量")
            return False
            
    except Exception as e:
        print(f"ERROR: 分析失败: {e}")
        return False

def main():
    """主函数"""
    success1 = compare_recent_predictions()
    success2 = analyze_prediction_patterns()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    
    if success1 and success2:
        print("✓ 多源风速数据正在有效工作")
        print("✓ 预测结果显示合理的变化")
        print("✓ 系统正常运行")
        print("\n建议:")
        print("- 继续使用当前配置")
        print("- 可以通过更多训练数据进一步优化")
        print("- 监控长期预测精度")
    elif success1:
        print("✓ 多源风速数据有一定影响")
        print("⚠ 预测模式需要进一步优化")
        print("\n建议:")
        print("- 检查特征工程参数")
        print("- 考虑调整模型权重")
        print("- 增加训练数据量")
    elif success2:
        print("⚠ 多源风速数据影响较小")
        print("✓ 预测模式基本正常")
        print("\n建议:")
        print("- 检查多源数据的时间对齐")
        print("- 验证特征工程流程")
        print("- 考虑重新训练模型")
    else:
        print("⚠ 需要进一步调试")
        print("\n建议:")
        print("- 检查数据文件完整性")
        print("- 验证配置设置")
        print("- 重新运行训练流程")
    
    return success1 or success2

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    print("分析完成")
    print("="*60)
